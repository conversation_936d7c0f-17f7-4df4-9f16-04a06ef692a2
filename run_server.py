#!/usr/bin/env python3
"""Simple server runner without debug mode."""
import os
from app import create_app

if __name__ == '__main__':
    app = create_app()
    print("🚀 Starting Unity Asset Catalog API...")
    print("📖 API Documentation: http://localhost:5000/api/docs")
    print("📊 Catalog Endpoint: http://localhost:5000/catalog")
    print("🔐 Login Endpoint: http://localhost:5000/auth/login")
    print("\nPress Ctrl+C to stop the server")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False  # Disable debug mode to avoid hanging
    )
