# Unity Asset Catalog API Makefile

.PHONY: help install run dev test lint format clean seed docker-build docker-run docker-stop

# Default target
help:
	@echo "Unity Asset Catalog API"
	@echo ""
	@echo "Available commands:"
	@echo "  install      Install dependencies"
	@echo "  run          Run the application"
	@echo "  dev          Run in development mode"
	@echo "  test         Run tests"
	@echo "  lint         Run linting"
	@echo "  format       Format code"
	@echo "  clean        Clean up temporary files"
	@echo "  seed         Seed database with sample data"
	@echo "  docker-build Build Docker image"
	@echo "  docker-run   Run with Docker Compose"
	@echo "  docker-stop  Stop Docker Compose"

# Install dependencies
install:
	pip install -r requirements.txt

# Run application
run:
	python app.py

# Run in development mode
dev:
	FLASK_ENV=development python app.py

# Run tests
test:
	pytest -v --tb=short

# Run tests with coverage
test-cov:
	pytest --cov=app --cov-report=html --cov-report=term

# Run linting
lint:
	flake8 app tests
	black --check app tests

# Format code
format:
	black app tests
	isort app tests

# Clean up
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf .coverage

# Seed database
seed:
	python scripts/seed_data.py

# Docker commands
docker-build:
	docker build -t unity-catalog-api .

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

docker-logs:
	docker-compose logs -f

# Database commands
db-init:
	python -c "from app import create_app; from app.db import init_db; app = create_app(); init_db(app)"

db-reset:
	python -c "from app import create_app; from app.db import reset_db; app = create_app(); reset_db(app)"

# Development setup
setup-dev: install db-init seed
	@echo "Development environment ready!"
	@echo "Run 'make dev' to start the server"
