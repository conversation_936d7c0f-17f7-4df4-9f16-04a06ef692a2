"""Material schemas."""
from marshmallow import Schema, fields, validate, post_load
from .common import validate_key, validate_shader, validate_texture_urls


class MaterialSchema(Schema):
    """Schema for material responses."""
    id = fields.String(dump_only=True)
    key = fields.String(required=True, validate=validate_key)
    display_name = fields.String(required=True, validate=validate.Length(min=1, max=255))
    shader = fields.String(required=True, validate=validate_shader)
    tags = fields.List(fields.String(), load_default=list)
    properties = fields.Dict(load_default=dict)
    texture_urls = fields.Dict(load_default=dict, validate=validate_texture_urls)
    thumbnail_url = fields.Url(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class MaterialCreateSchema(Schema):
    """Schema for creating materials."""
    key = fields.String(required=True, validate=validate_key)
    display_name = fields.String(required=True, validate=validate.Length(min=1, max=255))
    shader = fields.String(load_default='URP/Lit', validate=validate_shader)
    tags = fields.List(fields.String(), load_default=list)
    properties = fields.Dict(load_default=dict)
    texture_urls = fields.Dict(load_default=dict, validate=validate_texture_urls)
    thumbnail_url = fields.Url(allow_none=True)
    
    @post_load
    def make_material(self, data, **kwargs):
        """Post-process material data."""
        # Ensure lists and dicts are not None
        data['tags'] = data.get('tags') or []
        data['properties'] = data.get('properties') or {}
        data['texture_urls'] = data.get('texture_urls') or {}
        return data


class MaterialUpdateSchema(Schema):
    """Schema for updating materials."""
    display_name = fields.String(validate=validate.Length(min=1, max=255))
    shader = fields.String(validate=validate_shader)
    tags = fields.List(fields.String())
    properties = fields.Dict()
    texture_urls = fields.Dict(validate=validate_texture_urls)
    thumbnail_url = fields.Url(allow_none=True)
    
    @post_load
    def make_material(self, data, **kwargs):
        """Post-process material data."""
        # Ensure lists and dicts are not None if provided
        if 'tags' in data and data['tags'] is None:
            data['tags'] = []
        if 'properties' in data and data['properties'] is None:
            data['properties'] = {}
        if 'texture_urls' in data and data['texture_urls'] is None:
            data['texture_urls'] = {}
        return data


class MaterialListSchema(Schema):
    """Schema for material list responses."""
    materials = fields.List(fields.Nested(MaterialSchema))
    pagination = fields.Dict()
    total = fields.Integer()
