"""Common schemas and validators."""
import re
from marshmallow import Schema, fields, validate, ValidationError
from flask import current_app


def validate_key(value):
    """Validate that key is slug-friendly."""
    if not re.match(r'^[A-Za-z0-9_/-]+$', value):
        raise ValidationError(
            'Key must contain only letters, numbers, underscores, hyphens, and forward slashes'
        )


def validate_shader(value):
    """Validate shader against allowed values."""
    if value not in current_app.config['VALID_SHADERS']:
        raise ValidationError(
            f'Shader must be one of: {", ".join(current_app.config["VALID_SHADERS"])}'
        )


def validate_pipeline(value):
    """Validate pipeline against allowed values."""
    if value not in current_app.config['VALID_PIPELINES']:
        raise ValidationError(
            f'Pipeline must be one of: {", ".join(current_app.config["VALID_PIPELINES"])}'
        )


def validate_texture_urls(value):
    """Validate texture URLs dictionary."""
    if not isinstance(value, dict):
        raise ValidationError('Texture URLs must be a dictionary')
    
    valid_maps = current_app.config['VALID_TEXTURE_MAPS']
    for key in value.keys():
        if key not in valid_maps:
            raise ValidationError(
                f'Invalid texture map "{key}". Valid maps: {", ".join(valid_maps)}'
            )


class PaginationSchema(Schema):
    """Schema for pagination parameters."""
    page = fields.Integer(load_default=1, validate=validate.Range(min=1))
    page_size = fields.Integer(
        load_default=50,
        validate=validate.Range(min=1, max=200)
    )


class ErrorSchema(Schema):
    """Schema for error responses."""
    error = fields.String(required=True)
    message = fields.String(required=True)
    details = fields.Raw(load_default=None)
