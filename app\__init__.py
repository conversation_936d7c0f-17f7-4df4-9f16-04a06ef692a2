"""Flask application factory."""
import logging
import os
from pathlib import Path

from flask import Flask
from flask_cors import CORS
from flask_jwt_extended import J<PERSON>TManager
from flask_migrate import Migrate
from flask_smorest import Api

from app.config import config
from app.db import db
from app.logging_config import setup_logging


def create_app(config_name=None):
    """Create Flask application."""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Setup logging
    setup_logging(app)
    
    # Initialize extensions
    db.init_app(app)
    migrate = Migrate(app, db)
    jwt = JWTManager(app)
    
    # Setup CORS
    CORS(app, origins=app.config['CORS_ORIGINS'])
    
    # Setup API documentation
    api = Api(app)
    
    # Create upload directory
    upload_dir = Path(app.config['UPLOAD_FOLDER'])
    upload_dir.mkdir(parents=True, exist_ok=True)
    
    # Register blueprints
    from app.routes.auth import blp as auth_blp
    from app.routes.materials import blp as materials_blp
    from app.routes.prefabs import blp as prefabs_blp
    from app.routes.catalog import blp as catalog_blp
    from app.routes.upload import blp as upload_blp
    
    api.register_blueprint(auth_blp)
    api.register_blueprint(materials_blp)
    api.register_blueprint(prefabs_blp)
    api.register_blueprint(catalog_blp)
    api.register_blueprint(upload_blp)
    
    # Error handlers
    from app.error_handlers import register_error_handlers
    register_error_handlers(app)
    
    # JWT error handlers
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return {'message': 'Token has expired'}, 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return {'message': 'Invalid token'}, 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return {'message': 'Authorization token required'}, 401
    
    return app
