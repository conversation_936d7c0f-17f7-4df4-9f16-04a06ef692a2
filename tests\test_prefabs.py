"""Test prefab endpoints."""
import pytest


def test_create_prefab_with_auto_material_creation(client, auth_headers, sample_prefab):
    """Test creating prefab with automatic material creation."""
    response = client.post('/prefabs', 
                          json=sample_prefab,
                          headers=auth_headers)
    
    assert response.status_code == 201
    data = response.json
    assert data['key'] == sample_prefab['key']
    assert len(data['material_slots']) == 2
    
    # Verify materials were auto-created
    for slot in sample_prefab['material_slots']:
        material_response = client.get(f'/materials/{slot["material_key"]}')
        assert material_response.status_code == 200


def test_create_prefab_with_existing_materials(client, auth_headers, sample_material, sample_prefab):
    """Test creating prefab with existing materials."""
    # Create material first
    client.post('/materials', json=sample_material, headers=auth_headers)
    
    # Update prefab to use existing material
    sample_prefab['material_slots'] = [
        {'slot_name': 'Frame', 'material_key': sample_material['key']}
    ]
    
    response = client.post('/prefabs', 
                          json=sample_prefab,
                          headers=auth_headers)
    
    assert response.status_code == 201


def test_create_duplicate_prefab(client, auth_headers, sample_prefab):
    """Test creating prefab with duplicate key."""
    # Create first prefab
    client.post('/prefabs', json=sample_prefab, headers=auth_headers)
    
    # Try to create duplicate
    response = client.post('/prefabs', 
                          json=sample_prefab,
                          headers=auth_headers)
    
    assert response.status_code == 409


def test_create_prefab_with_upsert(client, auth_headers, sample_prefab):
    """Test creating prefab with upsert flag."""
    # Create first prefab
    client.post('/prefabs', json=sample_prefab, headers=auth_headers)
    
    # Update and upsert
    sample_prefab['display_name'] = 'Updated Chair'
    response = client.post('/prefabs?upsert=true', 
                          json=sample_prefab,
                          headers=auth_headers)
    
    assert response.status_code == 201
    assert response.json['display_name'] == 'Updated Chair'


def test_get_prefabs_list(client, auth_headers, sample_prefab):
    """Test getting prefabs list."""
    client.post('/prefabs', json=sample_prefab, headers=auth_headers)
    
    response = client.get('/prefabs')
    
    assert response.status_code == 200
    data = response.json
    assert 'prefabs' in data
    assert 'pagination' in data
    assert len(data['prefabs']) == 1


def test_get_prefab_by_key(client, auth_headers, sample_prefab):
    """Test getting prefab by key."""
    client.post('/prefabs', json=sample_prefab, headers=auth_headers)
    
    response = client.get(f'/prefabs/{sample_prefab["key"]}')
    
    assert response.status_code == 200
    data = response.json
    assert data['key'] == sample_prefab['key']
    assert 'material_slots' in data


def test_update_prefab_material_slots(client, auth_headers, sample_prefab):
    """Test updating prefab material slots."""
    # Create prefab
    client.post('/prefabs', json=sample_prefab, headers=auth_headers)
    
    # Update with new material slots
    update_data = {
        'material_slots': [
            {'slot_name': 'NewSlot', 'material_key': 'New_Material_01'}
        ]
    }
    
    response = client.put(f'/prefabs/{sample_prefab["key"]}',
                         json=update_data,
                         headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json
    assert len(data['material_slots']) == 1
    assert data['material_slots'][0]['slot_name'] == 'NewSlot'
    
    # Verify new material was created
    material_response = client.get('/materials/New_Material_01')
    assert material_response.status_code == 200


def test_delete_prefab(client, auth_headers, sample_prefab):
    """Test deleting a prefab."""
    client.post('/prefabs', json=sample_prefab, headers=auth_headers)
    
    response = client.delete(f'/prefabs/{sample_prefab["key"]}',
                           headers=auth_headers)
    
    assert response.status_code == 204
    
    # Verify it's deleted
    response = client.get(f'/prefabs/{sample_prefab["key"]}')
    assert response.status_code == 404


def test_search_prefabs(client, auth_headers):
    """Test searching prefabs."""
    prefabs = [
        {
            'key': 'Furniture/Chair_01',
            'display_name': 'Modern Chair',
            'category': 'Furniture',
            'asset_url': 'https://example.com/chair.glb',
            'pipeline': 'URP'
        },
        {
            'key': 'Decor/Lamp_01',
            'display_name': 'Table Lamp',
            'category': 'Decor',
            'asset_url': 'https://example.com/lamp.glb',
            'pipeline': 'HDRP'
        }
    ]
    
    for prefab in prefabs:
        client.post('/prefabs', json=prefab, headers=auth_headers)
    
    # Search by category
    response = client.get('/prefabs?category=Furniture')
    assert response.status_code == 200
    assert len(response.json['prefabs']) == 1
    
    # Search by pipeline
    response = client.get('/prefabs?pipeline=HDRP')
    assert response.status_code == 200
    assert len(response.json['prefabs']) == 1
