#!/usr/bin/env python3
"""Basic test to verify the application works."""
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.db import init_db
    
    print("✓ Successfully imported Flask app")
    
    # Create app
    app = create_app('testing')
    print("✓ Successfully created Flask app")
    
    # Test app context
    with app.app_context():
        init_db(app)
        print("✓ Successfully initialized database")
    
    # Test client
    client = app.test_client()
    
    # Test basic endpoint
    response = client.get('/catalog')
    print(f"✓ Catalog endpoint responded with status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.get_json()
        print(f"✓ Catalog contains {len(data.get('materials', []))} materials and {len(data.get('prefabs', []))} prefabs")
    
    print("\n🎉 Basic functionality test passed!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required dependencies:")
    print("pip install Flask Flask-SQLAlchemy Flask-CORS Flask-JWT-Extended flask-smorest marshmallow python-dotenv")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
