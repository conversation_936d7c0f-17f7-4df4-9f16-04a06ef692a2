"""Prefab API routes."""
from flask import request
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from flask_jwt_extended import jwt_required
from marshmallow import Schema, fields, validate

from app.auth import api_key_or_jwt_required
from app.services.prefab_service import PrefabService
from app.schemas.prefab import (
    PrefabSchema, 
    PrefabCreateSchema, 
    PrefabUpdateSchema,
    PrefabListSchema,
    PrefabQuerySchema
)

blp = Blueprint('prefabs', __name__, url_prefix='/prefabs', description='Prefabs')


@blp.route('/')
class PrefabList(MethodView):
    """Prefab list endpoint."""
    
    @blp.arguments(PrefabQuerySchema, location='query')
    @blp.response(200, PrefabListSchema)
    def get(self, query_args):
        """Get list of prefabs with optional search and pagination."""
        result = PrefabService.get_prefabs(**query_args)
        return result
    
    @jwt_required()
    @blp.arguments(PrefabCreateSchema)
    @blp.response(201, PrefabSchema)
    @blp.alt_response(409, description='Prefab with this key already exists')
    def post(self, prefab_data):
        """Create a new prefab."""
        # Check for upsert parameter
        upsert = request.args.get('upsert', 'false').lower() == 'true'
        
        existing = PrefabService.get_prefab_by_key(prefab_data['key'])
        if existing:
            if upsert:
                # Update existing prefab
                updated_prefab = PrefabService.update_prefab(existing, prefab_data)
                return updated_prefab.to_dict()
            else:
                abort(409, message=f'Prefab with key "{prefab_data["key"]}" already exists')
        
        prefab = PrefabService.create_prefab(prefab_data)
        return prefab.to_dict()


@blp.route('/<string:key>')
class PrefabDetail(MethodView):
    """Prefab detail endpoint."""
    
    @api_key_or_jwt_required
    @blp.response(200, PrefabSchema)
    @blp.alt_response(404, description='Prefab not found')
    def get(self, key):
        """Get prefab by key."""
        prefab = PrefabService.get_prefab_by_key(key)
        if not prefab:
            abort(404, message=f'Prefab with key "{key}" not found')
        return prefab.to_dict()
    
    @jwt_required()
    @blp.arguments(PrefabUpdateSchema)
    @blp.response(200, PrefabSchema)
    @blp.alt_response(404, description='Prefab not found')
    def put(self, prefab_data, key):
        """Update prefab by key."""
        prefab = PrefabService.get_prefab_by_key(key)
        if not prefab:
            abort(404, message=f'Prefab with key "{key}" not found')
        
        updated_prefab = PrefabService.update_prefab(prefab, prefab_data)
        return updated_prefab.to_dict()
    
    @jwt_required()
    @blp.response(204)
    @blp.alt_response(404, description='Prefab not found')
    def delete(self, key):
        """Delete prefab by key."""
        prefab = PrefabService.get_prefab_by_key(key)
        if not prefab:
            abort(404, message=f'Prefab with key "{key}" not found')
        
        PrefabService.delete_prefab(prefab)
        return ''
