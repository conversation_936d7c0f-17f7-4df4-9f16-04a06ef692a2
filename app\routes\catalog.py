"""Public catalog API routes."""
from flask.views import MethodView
from flask_smorest import Blueprint
from marshmallow import Schema, fields

from app.auth import api_key_or_jwt_required
from app.services.catalog_service import CatalogService

blp = Blueprint('catalog', __name__, url_prefix='/catalog', description='Public Catalog')


class MaterialCatalogSchema(Schema):
    """Schema for catalog material."""
    key = fields.String()
    display_name = fields.String()
    shader = fields.String()
    thumbnail_url = fields.String(allow_none=True)


class MaterialSlotCatalogSchema(Schema):
    """Schema for catalog material slot."""
    slot_name = fields.String()
    material_key = fields.String()


class PrefabCatalogSchema(Schema):
    """Schema for catalog prefab."""
    key = fields.String()
    display_name = fields.String()
    category = fields.String()
    preview_url = fields.String(allow_none=True)
    asset_url = fields.String()
    pipeline = fields.String()
    material_slots = fields.List(fields.Nested(MaterialSlotCatalogSchema))


class CatalogSchema(Schema):
    """Schema for complete catalog."""
    materials = fields.List(fields.Nested(MaterialCatalogSchema))
    prefabs = fields.List(fields.Nested(PrefabCatalogSchema))
    version = fields.String()
    generated_at = fields.String()


@blp.route('/')
class Catalog(MethodView):
    """Public catalog endpoint."""
    
    @blp.response(200, CatalogSchema)
    def get(self):
        """Get complete catalog for Unity client."""
        catalog = CatalogService.get_catalog()
        return catalog
