"""Material model."""
import uuid
from datetime import datetime
from sqlalchemy import Column, String, Text, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.dialects.sqlite import <PERSON>SO<PERSON> as SQLiteJSON
from app.db import db


class Material(db.Model):
    """Material model for Unity assets."""
    
    __tablename__ = 'materials'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    key = Column(String(255), unique=True, nullable=False, index=True)
    display_name = Column(String(255), nullable=False)
    shader = Column(String(100), nullable=False, default='URP/Lit')
    tags = Column(JSON, nullable=False, default=list)
    properties = Column(JSON, nullable=False, default=dict)
    texture_urls = Column(JSON, nullable=False, default=dict)
    thumbnail_url = Column(Text, nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(
        DateTime, 
        nullable=False, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow
    )
    
    def __repr__(self):
        return f'<Material {self.key}>'
    
    def to_dict(self):
        """Convert to dictionary."""
        return {
            'id': self.id,
            'key': self.key,
            'display_name': self.display_name,
            'shader': self.shader,
            'tags': self.tags or [],
            'properties': self.properties or {},
            'texture_urls': self.texture_urls or {},
            'thumbnail_url': self.thumbnail_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
    
    @classmethod
    def create_default(cls, key):
        """Create a material with default values."""
        return cls(
            key=key,
            display_name=key,
            shader='URP/Lit',
            tags=[],
            properties={},
            texture_urls={}
        )
