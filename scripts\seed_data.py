#!/usr/bin/env python3
"""Seed database with sample data."""
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from app.db import db, init_db
from app.models import Material, Prefab, PrefabMaterialSlot


def seed_materials():
    """Create sample materials."""
    materials = [
        {
            'key': 'Wood_Oak_01',
            'display_name': 'Oak Wood',
            'shader': 'URP/Lit',
            'tags': ['wood', 'natural', 'brown'],
            'properties': {
                '_Smoothness': 0.3,
                '_Metallic': 0.0
            },
            'texture_urls': {
                'albedo': 'https://cdn.example.com/textures/wood_oak_albedo.png',
                'normal': 'https://cdn.example.com/textures/wood_oak_normal.png',
                'roughness': 'https://cdn.example.com/textures/wood_oak_roughness.png'
            },
            'thumbnail_url': 'https://cdn.example.com/thumbnails/wood_oak.png'
        },
        {
            'key': 'Fabric_Grey_02',
            'display_name': 'Grey Fabric',
            'shader': 'URP/Lit',
            'tags': ['fabric', 'soft', 'grey'],
            'properties': {
                '_Smoothness': 0.1,
                '_Metallic': 0.0
            },
            'texture_urls': {
                'albedo': 'https://cdn.example.com/textures/fabric_grey_albedo.png',
                'normal': 'https://cdn.example.com/textures/fabric_grey_normal.png'
            },
            'thumbnail_url': 'https://cdn.example.com/thumbnails/fabric_grey.png'
        },
        {
            'key': 'Metal_Chrome_01',
            'display_name': 'Chrome Metal',
            'shader': 'URP/Lit',
            'tags': ['metal', 'shiny', 'chrome'],
            'properties': {
                '_Smoothness': 0.9,
                '_Metallic': 1.0
            },
            'texture_urls': {
                'albedo': 'https://cdn.example.com/textures/chrome_albedo.png',
                'metallic': 'https://cdn.example.com/textures/chrome_metallic.png'
            },
            'thumbnail_url': 'https://cdn.example.com/thumbnails/chrome.png'
        },
        {
            'key': 'Glass_Clear_01',
            'display_name': 'Clear Glass',
            'shader': 'URP/Lit',
            'tags': ['glass', 'transparent', 'clear'],
            'properties': {
                '_Smoothness': 1.0,
                '_Metallic': 0.0,
                '_Alpha': 0.3
            },
            'texture_urls': {
                'albedo': 'https://cdn.example.com/textures/glass_albedo.png'
            },
            'thumbnail_url': 'https://cdn.example.com/thumbnails/glass.png'
        }
    ]
    
    for material_data in materials:
        existing = Material.query.filter_by(key=material_data['key']).first()
        if not existing:
            material = Material(**material_data)
            db.session.add(material)
    
    db.session.commit()
    print(f"Created {len(materials)} materials")


def seed_prefabs():
    """Create sample prefabs."""
    prefabs_data = [
        {
            'prefab': {
                'key': 'Furniture/Chair_Classic_03',
                'display_name': 'Classic Chair',
                'category': 'Furniture',
                'description': 'A classic wooden chair with fabric upholstery',
                'preview_url': 'https://cdn.example.com/previews/chair_classic.png',
                'asset_url': 'https://cdn.example.com/assets/chair_classic.glb',
                'pipeline': 'URP'
            },
            'slots': [
                {'slot_name': 'Seat', 'material_key': 'Fabric_Grey_02'},
                {'slot_name': 'Frame', 'material_key': 'Wood_Oak_01'}
            ]
        },
        {
            'prefab': {
                'key': 'Furniture/Table_Modern_01',
                'display_name': 'Modern Table',
                'category': 'Furniture',
                'description': 'A modern glass table with chrome legs',
                'preview_url': 'https://cdn.example.com/previews/table_modern.png',
                'asset_url': 'https://cdn.example.com/assets/table_modern.glb',
                'pipeline': 'URP'
            },
            'slots': [
                {'slot_name': 'Top', 'material_key': 'Glass_Clear_01'},
                {'slot_name': 'Legs', 'material_key': 'Metal_Chrome_01'}
            ]
        },
        {
            'prefab': {
                'key': 'Decor/Lamp_Desk_01',
                'display_name': 'Desk Lamp',
                'category': 'Decor',
                'description': 'A modern desk lamp with adjustable arm',
                'preview_url': 'https://cdn.example.com/previews/lamp_desk.png',
                'asset_url': 'https://cdn.example.com/assets/lamp_desk.glb',
                'pipeline': 'HDRP'
            },
            'slots': [
                {'slot_name': 'Base', 'material_key': 'Metal_Chrome_01'},
                {'slot_name': 'Shade', 'material_key': 'Fabric_Grey_02'}
            ]
        }
    ]
    
    for item in prefabs_data:
        prefab_data = item['prefab']
        slots_data = item['slots']
        
        existing = Prefab.query.filter_by(key=prefab_data['key']).first()
        if not existing:
            # Create prefab
            prefab = Prefab(**prefab_data)
            db.session.add(prefab)
            db.session.flush()  # Get the ID
            
            # Create material slots
            for slot_data in slots_data:
                slot = PrefabMaterialSlot(
                    prefab_id=prefab.id,
                    slot_name=slot_data['slot_name'],
                    material_key=slot_data['material_key']
                )
                db.session.add(slot)
    
    db.session.commit()
    print(f"Created {len(prefabs_data)} prefabs")


def main():
    """Main seeding function."""
    app = create_app()
    
    with app.app_context():
        # Initialize database if needed
        init_db(app)
        
        print("Seeding database with sample data...")
        seed_materials()
        seed_prefabs()
        print("Database seeding completed!")


if __name__ == '__main__':
    main()
