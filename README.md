# Unity Asset Catalog API

A production-ready Flask API for managing Unity prefabs and materials catalog. This API provides comprehensive CRUD operations for materials and prefabs, with automatic material creation, file uploads, and a public catalog endpoint for Unity client consumption.

## Features

- **Material Management**: Create, read, update, delete materials with validation
- **Prefab Management**: Manage prefabs with automatic material slot handling
- **Auto-upsert**: Automatically create missing materials when creating/updating prefabs
- **Search & Pagination**: Full-text search with pagination support
- **File Uploads**: Local and S3 storage support for thumbnails and assets
- **Public Catalog**: Read-only endpoint for Unity client integration
- **JWT Authentication**: Secure API with Bearer token authentication
- **OpenAPI Documentation**: Interactive API docs at `/api/docs`
- **Docker Support**: Containerized deployment with PostgreSQL
- **Comprehensive Testing**: Full test suite with pytest

## Quick Start

### Local Development

1. **Clone and setup**:
```bash
git clone <repository-url>
cd unity-asset-catalog-api
cp .env.example .env
```

2. **Install dependencies**:
```bash
make install
# or
pip install -r requirements.txt
```

3. **Setup database and seed data**:
```bash
make setup-dev
# or manually:
make db-init
make seed
```

4. **Run the application**:
```bash
make dev
# or
python app.py
```

The API will be available at `http://localhost:5000` with documentation at `http://localhost:5000/api/docs`.

### Docker Deployment

1. **Build and run with Docker Compose**:
```bash
make docker-run
# or
docker-compose up -d
```

2. **View logs**:
```bash
make docker-logs
```

3. **Stop services**:
```bash
make docker-stop
```

## API Endpoints

### Authentication
- `POST /auth/login` - Login and get JWT token

### Materials
- `GET /materials` - List materials (public, supports search & pagination)
- `POST /materials` - Create material (requires auth)
- `GET /materials/{key}` - Get material by key (public)
- `PUT /materials/{key}` - Update material (requires auth)
- `DELETE /materials/{key}` - Delete material (requires auth)

### Prefabs
- `GET /prefabs` - List prefabs (public, supports search & pagination)
- `POST /prefabs` - Create prefab (requires auth, auto-creates materials)
- `GET /prefabs/{key}` - Get prefab by key (public)
- `PUT /prefabs/{key}` - Update prefab (requires auth, auto-creates materials)
- `DELETE /prefabs/{key}` - Delete prefab (requires auth)

### Catalog
- `GET /catalog` - Get complete catalog for Unity client (public)

### File Upload
- `POST /upload` - Upload files (requires auth)

## Configuration

### Environment Variables

```bash
# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
DEBUG=true

# Database
DATABASE_URL=sqlite:///unity_catalog.db
# For PostgreSQL: DATABASE_URL=postgresql://user:password@localhost/unity_catalog

# JWT
JWT_SECRET=your-jwt-secret-here

# File Storage
# For S3 storage:
S3_BUCKET=your-s3-bucket
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# CORS
CORS_ORIGINS=*

# API Keys
READ_API_KEY=your-read-only-api-key

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
```

## Usage Examples

### Authentication

```bash
# Login to get JWT token
curl -X POST http://localhost:5000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# Response:
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "Bearer",
  "expires_in": 86400
}
```

### Create Material

```bash
curl -X POST http://localhost:5000/materials \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "key": "Wood_Pine_01",
    "display_name": "Pine Wood",
    "shader": "URP/Lit",
    "tags": ["wood", "natural"],
    "properties": {"_Smoothness": 0.4},
    "texture_urls": {"albedo": "https://cdn.example.com/pine_albedo.png"},
    "thumbnail_url": "https://cdn.example.com/pine_thumb.png"
  }'
```

### Create Prefab with Auto-Material Creation

```bash
curl -X POST http://localhost:5000/prefabs \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "key": "Furniture/Bookshelf_01",
    "display_name": "Modern Bookshelf",
    "category": "Furniture",
    "description": "A modern wooden bookshelf",
    "preview_url": "https://cdn.example.com/bookshelf_preview.png",
    "asset_url": "https://cdn.example.com/bookshelf.glb",
    "pipeline": "URP",
    "material_slots": [
      {"slot_name": "Shelves", "material_key": "Wood_Pine_01"},
      {"slot_name": "Back", "material_key": "Wood_Dark_01"}
    ]
  }'
```

### Search Materials

```bash
# Search by name
curl "http://localhost:5000/materials?q=wood&page=1&page_size=10"

# Filter by tag
curl "http://localhost:5000/materials?tag=natural"
```

### Get Public Catalog

```bash
curl http://localhost:5000/catalog
```

### Upload File

```bash
curl -X POST http://localhost:5000/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@texture.png" \
  -F "subfolder=textures"
```

## Data Models

### Material
- `id` (UUID) - Primary key
- `key` (string) - Unique slug identifier
- `display_name` (string) - Human-readable name
- `shader` (string) - Shader type (BuiltIn/Standard, URP/Lit, HDRP/Lit)
- `tags` (array) - Searchable tags
- `properties` (JSON) - Shader properties (color, roughness, etc.)
- `texture_urls` (JSON) - Texture map URLs (albedo, normal, etc.)
- `thumbnail_url` (string) - Preview thumbnail URL
- `created_at`, `updated_at` (timestamps)

### Prefab
- `id` (UUID) - Primary key
- `key` (string) - Unique slug identifier
- `display_name` (string) - Human-readable name
- `category` (string) - Category (Furniture, Decor, etc.)
- `description` (string) - Description
- `preview_url` (string) - Preview image URL
- `asset_url` (string) - Asset file URL (GLB, bundle, etc.)
- `pipeline` (string) - Render pipeline (BuiltIn, URP, HDRP)
- `material_slots` (relationship) - Associated material slots
- `created_at`, `updated_at` (timestamps)

### PrefabMaterialSlot
- `id` (UUID) - Primary key
- `prefab_id` (UUID) - Foreign key to prefab
- `slot_name` (string) - Slot identifier
- `material_key` (string) - Foreign key to material
- `created_at`, `updated_at` (timestamps)

## Development

### Running Tests

```bash
# Run all tests
make test

# Run with coverage
make test-cov

# Run specific test file
pytest tests/test_materials.py -v
```

### Code Quality

```bash
# Format code
make format

# Run linting
make lint
```

### Database Operations

```bash
# Initialize database
make db-init

# Reset database
make db-reset

# Seed with sample data
make seed
```

## Deployment

### Production Checklist

1. **Environment Variables**: Set production values for all environment variables
2. **Database**: Use PostgreSQL in production
3. **Secrets**: Generate secure random values for `SECRET_KEY` and `JWT_SECRET`
4. **CORS**: Configure `CORS_ORIGINS` for your domain
5. **File Storage**: Configure S3 for file uploads
6. **Logging**: Set `LOG_LEVEL=INFO` and `LOG_FORMAT=json`
7. **SSL**: Use HTTPS in production

### Docker Production

```bash
# Build production image
docker build -t unity-catalog-api:latest .

# Run with production environment
docker-compose -f docker-compose.yml up -d
```

## Security

- JWT tokens expire after 24 hours
- All write operations require authentication
- Read operations are public (with optional API key)
- File uploads are restricted by type and size
- Input validation on all endpoints
- SQL injection protection via SQLAlchemy ORM

## Default Credentials

For development/testing:
- Username: `admin`, Password: `admin123`
- Username: `editor`, Password: `editor123`

**⚠️ Change these in production!**

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions, please create an issue in the repository.
