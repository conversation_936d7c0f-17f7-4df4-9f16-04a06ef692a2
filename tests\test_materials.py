"""Test material endpoints."""
import pytest


def test_create_material(client, auth_headers, sample_material):
    """Test creating a material."""
    response = client.post('/materials', 
                          json=sample_material,
                          headers=auth_headers)
    
    assert response.status_code == 201
    data = response.json
    assert data['key'] == sample_material['key']
    assert data['display_name'] == sample_material['display_name']
    assert 'id' in data
    assert 'created_at' in data


def test_create_duplicate_material(client, auth_headers, sample_material):
    """Test creating a material with duplicate key."""
    # Create first material
    client.post('/materials', json=sample_material, headers=auth_headers)
    
    # Try to create duplicate
    response = client.post('/materials', 
                          json=sample_material,
                          headers=auth_headers)
    
    assert response.status_code == 409


def test_get_materials_list(client, auth_headers, sample_material):
    """Test getting materials list."""
    # Create a material first
    client.post('/materials', json=sample_material, headers=auth_headers)
    
    response = client.get('/materials')
    
    assert response.status_code == 200
    data = response.json
    assert 'materials' in data
    assert 'pagination' in data
    assert 'total' in data
    assert len(data['materials']) == 1


def test_get_material_by_key(client, auth_headers, sample_material):
    """Test getting material by key."""
    # Create material first
    client.post('/materials', json=sample_material, headers=auth_headers)
    
    response = client.get(f'/materials/{sample_material["key"]}')
    
    assert response.status_code == 200
    data = response.json
    assert data['key'] == sample_material['key']


def test_get_nonexistent_material(client):
    """Test getting nonexistent material."""
    response = client.get('/materials/nonexistent')
    
    assert response.status_code == 404


def test_update_material(client, auth_headers, sample_material):
    """Test updating a material."""
    # Create material first
    client.post('/materials', json=sample_material, headers=auth_headers)
    
    update_data = {'display_name': 'Updated Oak Wood'}
    response = client.put(f'/materials/{sample_material["key"]}',
                         json=update_data,
                         headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json
    assert data['display_name'] == 'Updated Oak Wood'


def test_delete_material(client, auth_headers, sample_material):
    """Test deleting a material."""
    # Create material first
    client.post('/materials', json=sample_material, headers=auth_headers)
    
    response = client.delete(f'/materials/{sample_material["key"]}',
                           headers=auth_headers)
    
    assert response.status_code == 204
    
    # Verify it's deleted
    response = client.get(f'/materials/{sample_material["key"]}')
    assert response.status_code == 404


def test_search_materials(client, auth_headers):
    """Test searching materials."""
    # Create test materials
    materials = [
        {'key': 'Wood_Oak_01', 'display_name': 'Oak Wood', 'tags': ['wood']},
        {'key': 'Metal_Steel_01', 'display_name': 'Steel Metal', 'tags': ['metal']},
        {'key': 'Wood_Pine_01', 'display_name': 'Pine Wood', 'tags': ['wood']}
    ]
    
    for material in materials:
        client.post('/materials', json=material, headers=auth_headers)
    
    # Search by name
    response = client.get('/materials?q=Oak')
    assert response.status_code == 200
    assert len(response.json['materials']) == 1
    
    # Search by tag
    response = client.get('/materials?tag=wood')
    assert response.status_code == 200
    assert len(response.json['materials']) == 2


def test_pagination(client, auth_headers):
    """Test materials pagination."""
    # Create multiple materials
    for i in range(5):
        material = {
            'key': f'Material_{i:02d}',
            'display_name': f'Material {i}'
        }
        client.post('/materials', json=material, headers=auth_headers)
    
    # Test pagination
    response = client.get('/materials?page=1&page_size=2')
    assert response.status_code == 200
    data = response.json
    assert len(data['materials']) == 2
    assert data['pagination']['page'] == 1
    assert data['pagination']['total_pages'] == 3
