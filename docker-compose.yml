version: '3.8'

services:
  api:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=******************************************/unity_catalog
      - SECRET_KEY=production-secret-key-change-me
      - JWT_SECRET=production-jwt-secret-change-me
      - READ_API_KEY=production-api-key-change-me
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
    volumes:
      - ./data:/app/data
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=unity_catalog
      - POSTGRES_USER=unity_user
      - POSTGRES_PASSWORD=unity_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Optional: Redis for caching (future enhancement)
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   restart: unless-stopped

volumes:
  postgres_data:
