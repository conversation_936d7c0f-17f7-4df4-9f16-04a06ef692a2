"""Test configuration and fixtures."""
import pytest
import tempfile
import os
from app import create_app
from app.db import db, init_db


@pytest.fixture
def app():
    """Create application for testing."""
    # Create temporary database
    db_fd, db_path = tempfile.mkstemp()
    
    app = create_app('testing')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    
    with app.app_context():
        init_db(app)
        yield app
    
    # Cleanup
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def auth_headers(client):
    """Get authentication headers."""
    # Login to get token
    response = client.post('/auth/login', json={
        'username': 'admin',
        'password': 'admin123'
    })
    
    assert response.status_code == 200
    token = response.json['access_token']
    
    return {'Authorization': f'Bearer {token}'}


@pytest.fixture
def sample_material():
    """Sample material data."""
    return {
        'key': 'Wood_Oak_01',
        'display_name': 'Oak Wood',
        'shader': 'URP/Lit',
        'tags': ['wood', 'natural'],
        'properties': {'_Smoothness': 0.3},
        'texture_urls': {'albedo': 'https://example.com/oak_albedo.png'},
        'thumbnail_url': 'https://example.com/oak_thumb.png'
    }


@pytest.fixture
def sample_prefab():
    """Sample prefab data."""
    return {
        'key': 'Furniture/Chair_Classic_01',
        'display_name': 'Classic Chair',
        'category': 'Furniture',
        'description': 'A classic wooden chair',
        'preview_url': 'https://example.com/chair_preview.png',
        'asset_url': 'https://example.com/chair.glb',
        'pipeline': 'URP',
        'material_slots': [
            {'slot_name': 'Seat', 'material_key': 'Fabric_Grey_01'},
            {'slot_name': 'Frame', 'material_key': 'Wood_Oak_01'}
        ]
    }
