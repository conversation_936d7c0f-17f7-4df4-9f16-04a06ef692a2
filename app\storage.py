"""File storage utilities."""
import os
import uuid
from pathlib import Path
from werkzeug.utils import secure_filename
from flask import current_app, url_for


def allowed_file(filename):
    """Check if file extension is allowed."""
    if '.' not in filename:
        return False
    
    extension = filename.rsplit('.', 1)[1].lower()
    return extension in current_app.config['ALLOWED_EXTENSIONS']


def generate_filename(original_filename):
    """Generate a unique filename while preserving extension."""
    if '.' in original_filename:
        name, ext = original_filename.rsplit('.', 1)
        return f"{uuid.uuid4().hex}.{ext.lower()}"
    else:
        return f"{uuid.uuid4().hex}"


def save_file_local(file, subfolder=''):
    """Save file to local storage."""
    if not allowed_file(file.filename):
        raise ValueError(f"File type not allowed: {file.filename}")
    
    # Generate unique filename
    filename = generate_filename(file.filename)
    
    # Create subfolder if specified
    upload_path = Path(current_app.config['UPLOAD_FOLDER'])
    if subfolder:
        upload_path = upload_path / subfolder
        upload_path.mkdir(parents=True, exist_ok=True)
    
    # Save file
    file_path = upload_path / filename
    file.save(str(file_path))
    
    # Return URL
    if subfolder:
        return f"/data/assets/{subfolder}/{filename}"
    else:
        return f"/data/assets/{filename}"


def save_file_s3(file, subfolder=''):
    """Save file to S3 storage."""
    try:
        import boto3
        from botocore.exceptions import ClientError
    except ImportError:
        raise ImportError("boto3 is required for S3 storage")
    
    if not allowed_file(file.filename):
        raise ValueError(f"File type not allowed: {file.filename}")
    
    # Generate unique filename
    filename = generate_filename(file.filename)
    
    # Create S3 key
    if subfolder:
        s3_key = f"{subfolder}/{filename}"
    else:
        s3_key = filename
    
    # Upload to S3
    s3_client = boto3.client(
        's3',
        aws_access_key_id=current_app.config['AWS_ACCESS_KEY_ID'],
        aws_secret_access_key=current_app.config['AWS_SECRET_ACCESS_KEY'],
        region_name=current_app.config['AWS_REGION']
    )
    
    try:
        s3_client.upload_fileobj(
            file,
            current_app.config['S3_BUCKET'],
            s3_key,
            ExtraArgs={'ACL': 'public-read'}
        )
        
        # Return S3 URL
        return f"https://{current_app.config['S3_BUCKET']}.s3.{current_app.config['AWS_REGION']}.amazonaws.com/{s3_key}"
    
    except ClientError as e:
        raise RuntimeError(f"Failed to upload to S3: {str(e)}")


def save_file(file, subfolder=''):
    """Save file using configured storage backend."""
    if current_app.config.get('S3_BUCKET'):
        return save_file_s3(file, subfolder)
    else:
        return save_file_local(file, subfolder)
