#!/usr/bin/env python3
"""Test login functionality."""
import requests
import json

# API base URL
BASE_URL = "http://localhost:5000"

def test_login(username, password):
    """Test login with given credentials."""
    print(f"🔐 Testing login for user: {username}")
    
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"Token: {data['access_token'][:50]}...")
            print(f"Token Type: {data['token_type']}")
            print(f"Expires In: {data['expires_in']} seconds")
            return data['access_token']
        else:
            print("❌ Login failed!")
            print(f"Error: {response.json()}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed! Make sure the server is running.")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_protected_endpoint(token):
    """Test accessing a protected endpoint with the token."""
    print(f"\n🔒 Testing protected endpoint...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test creating a material (protected endpoint)
    material_data = {
        "key": "Test_Material_01",
        "display_name": "Test Material",
        "shader": "URP/Lit",
        "tags": ["test"]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/materials",
            json=material_data,
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ Protected endpoint access successful!")
            print(f"Created material: {response.json()['key']}")
        else:
            print("❌ Protected endpoint access failed!")
            print(f"Error: {response.json()}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🧪 Unity Asset Catalog API - Login Test")
    print("=" * 50)
    
    # Test valid credentials
    print("\n1. Testing valid credentials:")
    token = test_login("admin", "admin123")
    
    if token:
        test_protected_endpoint(token)
    
    # Test invalid credentials
    print("\n2. Testing invalid credentials:")
    test_login("admin", "wrong_password")
    
    # Test another valid user
    print("\n3. Testing editor user:")
    test_login("editor", "editor123")
    
    print("\n✨ Test completed!")
