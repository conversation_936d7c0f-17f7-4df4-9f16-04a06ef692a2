"""Test catalog endpoint."""
import pytest


def test_get_catalog_empty(client):
    """Test getting empty catalog."""
    response = client.get('/catalog')
    
    assert response.status_code == 200
    data = response.json
    assert 'materials' in data
    assert 'prefabs' in data
    assert 'version' in data
    assert 'generated_at' in data
    assert len(data['materials']) == 0
    assert len(data['prefabs']) == 0


def test_get_catalog_with_data(client, auth_headers, sample_material, sample_prefab):
    """Test getting catalog with materials and prefabs."""
    # Create material and prefab
    client.post('/materials', json=sample_material, headers=auth_headers)
    client.post('/prefabs', json=sample_prefab, headers=auth_headers)
    
    response = client.get('/catalog')
    
    assert response.status_code == 200
    data = response.json
    
    # Check materials
    assert len(data['materials']) >= 1
    material = next((m for m in data['materials'] if m['key'] == sample_material['key']), None)
    assert material is not None
    assert material['display_name'] == sample_material['display_name']
    assert material['shader'] == sample_material['shader']
    
    # Check prefabs
    assert len(data['prefabs']) == 1
    prefab = data['prefabs'][0]
    assert prefab['key'] == sample_prefab['key']
    assert prefab['display_name'] == sample_prefab['display_name']
    assert 'material_slots' in prefab
    assert len(prefab['material_slots']) == 2


def test_catalog_material_slots_reference(client, auth_headers, sample_prefab):
    """Test that catalog shows correct material slot references."""
    # Create prefab (which auto-creates materials)
    client.post('/prefabs', json=sample_prefab, headers=auth_headers)
    
    response = client.get('/catalog')
    data = response.json
    
    # Check that materials were auto-created
    material_keys = [m['key'] for m in data['materials']]
    for slot in sample_prefab['material_slots']:
        assert slot['material_key'] in material_keys
    
    # Check prefab material slots
    prefab = data['prefabs'][0]
    slot_keys = [slot['material_key'] for slot in prefab['material_slots']]
    for slot in sample_prefab['material_slots']:
        assert slot['material_key'] in slot_keys


def test_catalog_version_format(client):
    """Test catalog version format."""
    response = client.get('/catalog')
    data = response.json
    
    # Version should be in YYYY.MM.DD format
    import re
    version_pattern = r'^\d{4}\.\d{2}\.\d{2}$'
    assert re.match(version_pattern, data['version'])
    
    # Generated_at should be ISO format
    iso_pattern = r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z$'
    assert re.match(iso_pattern, data['generated_at'])
