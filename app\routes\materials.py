"""Material API routes."""
from flask import request
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from flask_jwt_extended import jwt_required
from marshmallow import Schema, fields, validate

from app.auth import api_key_or_jwt_required
from app.services.material_service import MaterialService
from app.schemas.material import (
    MaterialSchema, 
    MaterialCreateSchema, 
    MaterialUpdateSchema,
    MaterialListSchema
)

blp = Blueprint('materials', __name__, url_prefix='/materials', description='Materials')


class MaterialQuerySchema(Schema):
    """Schema for material query parameters."""
    q = fields.String(load_default=None)
    tag = fields.String(load_default=None)
    page = fields.Integer(load_default=1, validate=validate.Range(min=1))
    page_size = fields.Integer(load_default=50, validate=validate.Range(min=1, max=200))


@blp.route('/')
class MaterialList(MethodView):
    """Material list endpoint."""
    
    @blp.arguments(MaterialQuerySchema, location='query')
    @blp.response(200, MaterialListSchema)
    def get(self, query_args):
        """Get list of materials with optional search and pagination."""
        result = MaterialService.get_materials(**query_args)
        return result
    
    @jwt_required()
    @blp.arguments(MaterialCreateSchema)
    @blp.response(201, MaterialSchema)
    @blp.alt_response(409, description='Material with this key already exists')
    def post(self, material_data):
        """Create a new material."""
        # Check if material already exists
        existing = MaterialService.get_material_by_key(material_data['key'])
        if existing:
            abort(409, message=f'Material with key "{material_data["key"]}" already exists')
        
        material = MaterialService.create_material(material_data)
        return material.to_dict()


@blp.route('/<string:key>')
class MaterialDetail(MethodView):
    """Material detail endpoint."""
    
    @api_key_or_jwt_required
    @blp.response(200, MaterialSchema)
    @blp.alt_response(404, description='Material not found')
    def get(self, key):
        """Get material by key."""
        material = MaterialService.get_material_by_key(key)
        if not material:
            abort(404, message=f'Material with key "{key}" not found')
        return material.to_dict()
    
    @jwt_required()
    @blp.arguments(MaterialUpdateSchema)
    @blp.response(200, MaterialSchema)
    @blp.alt_response(404, description='Material not found')
    def put(self, material_data, key):
        """Update material by key."""
        material = MaterialService.get_material_by_key(key)
        if not material:
            abort(404, message=f'Material with key "{key}" not found')
        
        updated_material = MaterialService.update_material(material, material_data)
        return updated_material.to_dict()
    
    @jwt_required()
    @blp.response(204)
    @blp.alt_response(404, description='Material not found')
    def delete(self, key):
        """Delete material by key."""
        material = MaterialService.get_material_by_key(key)
        if not material:
            abort(404, message=f'Material with key "{key}" not found')
        
        MaterialService.delete_material(material)
        return ''
