"""Prefab service for business logic."""
from sqlalchemy import or_
from flask import current_app
from app.db import db
from app.models import Prefab, PrefabMaterialSlot
from app.services.material_service import MaterialService


class PrefabService:
    """Service for prefab operations."""
    
    @staticmethod
    def get_prefabs(page=1, page_size=50, q=None, category=None, pipeline=None):
        """Get paginated list of prefabs with optional search."""
        query = Prefab.query
        
        # Apply search filters
        if q:
            search_term = f'%{q}%'
            query = query.filter(
                or_(
                    Prefab.display_name.ilike(search_term),
                    Prefab.key.ilike(search_term),
                    Prefab.description.ilike(search_term)
                )
            )
        
        if category:
            query = query.filter(Prefab.category.ilike(f'%{category}%'))
        
        if pipeline:
            query = query.filter(Prefab.pipeline == pipeline)
        
        # Apply pagination
        pagination = query.paginate(
            page=page,
            per_page=page_size,
            error_out=False
        )
        
        return {
            'prefabs': [prefab.to_dict() for prefab in pagination.items],
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            },
            'total': pagination.total
        }
    
    @staticmethod
    def get_prefab_by_key(key):
        """Get prefab by key."""
        return Prefab.query.filter_by(key=key).first()
    
    @staticmethod
    def create_prefab(data):
        """Create a new prefab with material auto-upsert."""
        material_slots_data = data.pop('material_slots', [])
        
        # Create prefab
        prefab = Prefab(**data)
        db.session.add(prefab)
        db.session.flush()  # Get the ID
        
        # Process material slots
        PrefabService._process_material_slots(prefab, material_slots_data)
        
        db.session.commit()
        return prefab
    
    @staticmethod
    def update_prefab(prefab, data):
        """Update an existing prefab with material auto-upsert."""
        material_slots_data = data.pop('material_slots', None)
        
        # Update prefab fields
        for key, value in data.items():
            setattr(prefab, key, value)
        
        # Update material slots if provided
        if material_slots_data is not None:
            # Remove existing slots
            PrefabMaterialSlot.query.filter_by(prefab_id=prefab.id).delete()
            
            # Add new slots
            PrefabService._process_material_slots(prefab, material_slots_data)
        
        db.session.commit()
        return prefab
    
    @staticmethod
    def delete_prefab(prefab):
        """Delete a prefab."""
        db.session.delete(prefab)
        db.session.commit()
    
    @staticmethod
    def _process_material_slots(prefab, material_slots_data):
        """Process material slots and auto-create missing materials."""
        for slot_data in material_slots_data:
            material_key = slot_data['material_key']
            
            # Ensure material exists (auto-create if missing)
            MaterialService.upsert_material(material_key)
            
            # Create material slot
            slot = PrefabMaterialSlot(
                prefab_id=prefab.id,
                slot_name=slot_data['slot_name'],
                material_key=material_key
            )
            db.session.add(slot)
