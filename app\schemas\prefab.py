"""Prefab schemas."""
from marshmallow import Schema, fields, validate, post_load
from .common import validate_key, validate_pipeline


class PrefabMaterialSlotSchema(Schema):
    """Schema for prefab material slots."""
    id = fields.String(dump_only=True)
    slot_name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    material_key = fields.String(required=True, validate=validate_key)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class PrefabSchema(Schema):
    """Schema for prefab responses."""
    id = fields.String(dump_only=True)
    key = fields.String(required=True, validate=validate_key)
    display_name = fields.String(required=True, validate=validate.Length(min=1, max=255))
    category = fields.String(required=True, validate=validate.Length(min=1, max=100))
    description = fields.String(allow_none=True)
    preview_url = fields.Url(allow_none=True)
    asset_url = fields.Url(required=True)
    pipeline = fields.String(required=True, validate=validate_pipeline)
    material_slots = fields.List(fields.Nested(PrefabMaterialSlotSchema), dump_only=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class PrefabCreateSchema(Schema):
    """Schema for creating prefabs."""
    key = fields.String(required=True, validate=validate_key)
    display_name = fields.String(required=True, validate=validate.Length(min=1, max=255))
    category = fields.String(required=True, validate=validate.Length(min=1, max=100))
    description = fields.String(allow_none=True)
    preview_url = fields.Url(allow_none=True)
    asset_url = fields.Url(required=True)
    pipeline = fields.String(load_default='URP', validate=validate_pipeline)
    material_slots = fields.List(
        fields.Nested(PrefabMaterialSlotSchema, exclude=['id', 'created_at', 'updated_at']),
        load_default=list
    )
    
    @post_load
    def make_prefab(self, data, **kwargs):
        """Post-process prefab data."""
        data['material_slots'] = data.get('material_slots') or []
        return data


class PrefabUpdateSchema(Schema):
    """Schema for updating prefabs."""
    display_name = fields.String(validate=validate.Length(min=1, max=255))
    category = fields.String(validate=validate.Length(min=1, max=100))
    description = fields.String(allow_none=True)
    preview_url = fields.Url(allow_none=True)
    asset_url = fields.Url()
    pipeline = fields.String(validate=validate_pipeline)
    material_slots = fields.List(
        fields.Nested(PrefabMaterialSlotSchema, exclude=['id', 'created_at', 'updated_at'])
    )
    
    @post_load
    def make_prefab(self, data, **kwargs):
        """Post-process prefab data."""
        if 'material_slots' in data and data['material_slots'] is None:
            data['material_slots'] = []
        return data


class PrefabListSchema(Schema):
    """Schema for prefab list responses."""
    prefabs = fields.List(fields.Nested(PrefabSchema))
    pagination = fields.Dict()
    total = fields.Integer()


class PrefabQuerySchema(Schema):
    """Schema for prefab query parameters."""
    q = fields.String(load_default=None)
    category = fields.String(load_default=None)
    pipeline = fields.String(load_default=None, validate=validate_pipeline)
    page = fields.Integer(load_default=1, validate=validate.Range(min=1))
    page_size = fields.Integer(load_default=50, validate=validate.Range(min=1, max=200))
