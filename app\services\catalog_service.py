"""Catalog service for public API."""
from datetime import datetime
from app.models import Material, Prefab


class CatalogService:
    """Service for catalog operations."""
    
    @staticmethod
    def get_catalog():
        """Get complete catalog for Unity client."""
        # Get all materials
        materials = Material.query.all()
        material_data = []
        for material in materials:
            material_data.append({
                'key': material.key,
                'display_name': material.display_name,
                'shader': material.shader,
                'thumbnail_url': material.thumbnail_url
            })
        
        # Get all prefabs with material slots
        prefabs = Prefab.query.all()
        prefab_data = []
        for prefab in prefabs:
            material_slots = []
            for slot in prefab.material_slots:
                material_slots.append({
                    'slot_name': slot.slot_name,
                    'material_key': slot.material_key
                })
            
            prefab_data.append({
                'key': prefab.key,
                'display_name': prefab.display_name,
                'category': prefab.category,
                'preview_url': prefab.preview_url,
                'asset_url': prefab.asset_url,
                'pipeline': prefab.pipeline,
                'material_slots': material_slots
            })
        
        return {
            'materials': material_data,
            'prefabs': prefab_data,
            'version': datetime.now().strftime('%Y.%m.%d'),
            'generated_at': datetime.utcnow().isoformat() + 'Z'
        }
