"""Prefab and PrefabMaterialSlot models."""
import uuid
from datetime import datetime
from sqlalchemy import Column, String, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.db import db


class Prefab(db.Model):
    """Prefab model for Unity assets."""
    
    __tablename__ = 'prefabs'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    key = Column(String(255), unique=True, nullable=False, index=True)
    display_name = Column(String(255), nullable=False)
    category = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    preview_url = Column(Text, nullable=True)
    asset_url = Column(Text, nullable=False)
    pipeline = Column(String(20), nullable=False, default='URP')
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(
        DateTime, 
        nullable=False, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow
    )
    
    # Relationship to material slots
    material_slots = relationship(
        'PrefabMaterialSlot',
        back_populates='prefab',
        cascade='all, delete-orphan'
    )
    
    def __repr__(self):
        return f'<Prefab {self.key}>'
    
    def to_dict(self, include_slots=True):
        """Convert to dictionary."""
        result = {
            'id': self.id,
            'key': self.key,
            'display_name': self.display_name,
            'category': self.category,
            'description': self.description,
            'preview_url': self.preview_url,
            'asset_url': self.asset_url,
            'pipeline': self.pipeline,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
        
        if include_slots:
            result['material_slots'] = [
                slot.to_dict() for slot in self.material_slots
            ]
        
        return result


class PrefabMaterialSlot(db.Model):
    """Material slot assignment for prefabs."""
    
    __tablename__ = 'prefab_material_slots'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    prefab_id = Column(String(36), ForeignKey('prefabs.id'), nullable=False)
    slot_name = Column(String(100), nullable=False)
    material_key = Column(
        String(255),
        ForeignKey('materials.key'),
        nullable=False
    )
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(
        DateTime, 
        nullable=False, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow
    )
    
    # Relationships
    prefab = relationship('Prefab', back_populates='material_slots')
    material = relationship('Material')
    
    # Unique constraint on prefab_id + slot_name
    __table_args__ = (
        db.UniqueConstraint('prefab_id', 'slot_name', name='uq_prefab_slot'),
    )
    
    def __repr__(self):
        return f'<PrefabMaterialSlot {self.slot_name}:{self.material_key}>'
    
    def to_dict(self):
        """Convert to dictionary."""
        return {
            'id': self.id,
            'slot_name': self.slot_name,
            'material_key': self.material_key,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
