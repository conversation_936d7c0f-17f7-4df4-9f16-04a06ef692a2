"""Test authentication endpoints."""
import pytest


def test_login_success(client):
    """Test successful login."""
    response = client.post('/auth/login', json={
        'username': 'admin',
        'password': 'admin123'
    })
    
    assert response.status_code == 200
    data = response.json
    assert 'access_token' in data
    assert data['token_type'] == 'Bearer'
    assert data['expires_in'] == 86400


def test_login_invalid_credentials(client):
    """Test login with invalid credentials."""
    response = client.post('/auth/login', json={
        'username': 'admin',
        'password': 'wrong_password'
    })
    
    assert response.status_code == 401
    assert 'Invalid username or password' in response.json['message']


def test_login_missing_fields(client):
    """Test login with missing fields."""
    response = client.post('/auth/login', json={
        'username': 'admin'
    })
    
    assert response.status_code == 422  # Validation error


def test_protected_endpoint_without_token(client):
    """Test accessing protected endpoint without token."""
    response = client.post('/materials', json={
        'key': 'test_material',
        'display_name': 'Test Material'
    })
    
    assert response.status_code == 401


def test_protected_endpoint_with_token(client, auth_headers, sample_material):
    """Test accessing protected endpoint with valid token."""
    response = client.post('/materials', 
                          json=sample_material,
                          headers=auth_headers)
    
    assert response.status_code == 201
