"""File upload API routes."""
from flask import request
from flask.views import Method<PERSON>iew
from flask_smorest import Blueprint, abort
from flask_jwt_extended import jwt_required
from marshmallow import Schema, fields
from werkzeug.datastructures import FileStorage

from app.storage import save_file

blp = Blueprint('upload', __name__, url_prefix='/upload', description='File Upload')


class UploadResponseSchema(Schema):
    """Schema for upload response."""
    url = fields.String()
    filename = fields.String()
    size = fields.Integer()


@blp.route('/')
class Upload(MethodView):
    """File upload endpoint."""
    
    @jwt_required()
    @blp.response(200, UploadResponseSchema)
    @blp.alt_response(400, description='No file provided or invalid file type')
    def post(self):
        """Upload a file."""
        # Check if file is present
        if 'file' not in request.files:
            abort(400, message='No file provided')
        
        file = request.files['file']
        if file.filename == '':
            abort(400, message='No file selected')
        
        # Get optional subfolder
        subfolder = request.form.get('subfolder', '')
        
        try:
            # Save file
            file_url = save_file(file, subfolder)
            
            # Get file size
            file.seek(0, 2)  # Seek to end
            file_size = file.tell()
            file.seek(0)  # Reset position
            
            return {
                'url': file_url,
                'filename': file.filename,
                'size': file_size
            }
        
        except ValueError as e:
            abort(400, message=str(e))
        except Exception as e:
            abort(500, message=f'Upload failed: {str(e)}')
